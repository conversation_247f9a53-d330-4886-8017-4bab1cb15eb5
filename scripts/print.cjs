const puppeteer = require("puppeteer");
const path = require("path");
const fs = require("fs").promises;

const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

async function generateFiles(url, filename) {
  const browser = await puppeteer.launch({
    args: [
      "--allow-file-access-from-files", // 禁用加载本地文件跨域
      "--no-sandbox",
    ],
  });

  try {
    const page = await browser.newPage();
    // 设置页面大小为A4，适合打印
    await page.setViewport({
      width: 800,
      height: 600,
      deviceScaleFactor: 2,
    });

    console.log(`Open: "${url}"`);
    await page.goto(url, { waitUntil: "networkidle2" }); // 等待页面完全加载
    await wait(500);

    await outputPDF(page, filename);
    await outputScreenshot(page, filename);
  } finally {
    await browser.close(); // 关闭浏览器
  }
}

async function outputPDF(page, name) {
  await page.pdf({
    path: name + ".pdf", // 输出PDF的路径
    format: "A4", // 纸张大小
    printBackground: true, // 是否包含背景颜色和图像
    margin: { top: "0cm", right: "0cm", bottom: "0cm", left: "0cm" }, // 边距设置
    scale: 0.88,
  });
}

async function outputScreenshot(page, filename) {
  await page.screenshot({
    path: filename + ".png",
    type: "png",
    fullPage: true,
  });
}

function getCurrentDate() {
  const currentDate = new Date();

  const year = currentDate.getFullYear().toString();
  const month = (currentDate.getMonth() + 1).toString().padStart(2, "0");
  const day = currentDate.getDate().toString().padStart(2, "0");

  return year + month + day;
}

const OUTPUT = path.resolve("output");

async function main() {
  try {
    await fs.access(OUTPUT, fs.constants.F_OK);
    await fs.rm(OUTPUT, { recursive: true });
  } catch {}
  await fs.mkdir(OUTPUT, { recursive: true });

  const htmlpath = path.join(__dirname, "../dist/index.html");
  const filename = `简历_李登科_开发_${getCurrentDate()}`;
  const pdffilepath = path.join(OUTPUT, filename);
  generateFiles(`file://${htmlpath}`, pdffilepath)
    .then(() => console.log(`Files generated successfully`))
    .catch((error) => console.error("Error:", error));
}

main();
