import "./index.css";
import { render } from "preact";
import { currentMonth, getMyAge } from "./helper";
import chunk from "lodash/chunk";
import { PropsWithChildren } from "preact/compat";
import { useEffect, useState } from "react";
import { IoLocationOutline } from "react-icons/io5";

function App() {
  return (
    <main className="container">
      <div>
        <h1>
          <strong>李登科</strong>
        </h1>
        <p>
          擅长 Web 前端与 Python 后端开发，具备从 0
          到部署的完整项目能力，注重可测试性和可维护性。
        </p>
      </div>
      <Profile />
      <Education />
      <WorkExperiences />
      <OpenSourceProjects />
      <Skills />
    </main>
  );
}

export function usePrintMode(): boolean {
  const [isPrintMode, setIsPrintMode] = useState(false);

  useEffect(() => {
    if (typeof window === "undefined") return;

    const mediaQueryList = window.matchMedia("print");

    const update = (e?: MediaQueryListEvent) => {
      setIsPrintMode(e ? e.matches : mediaQueryList.matches);
    };

    // 初始状态
    update();

    // 现代浏览器事件监听
    mediaQueryList.addEventListener?.("change", update);

    // 回退方案（兼容旧浏览器）
    window.addEventListener?.("beforeprint", () => setIsPrintMode(true));
    window.addEventListener?.("afterprint", () => setIsPrintMode(false));

    return () => {
      mediaQueryList.removeEventListener?.("change", update);
      window.removeEventListener?.("beforeprint", () => setIsPrintMode(true));
      window.removeEventListener?.("afterprint", () => setIsPrintMode(false));
    };
  }, []);

  return isPrintMode;
}

function Profile() {
  const homepage = "https://me.oneproject.dev";
  const githubLink = "https://github.com/dog-egg";
  const email = "<EMAIL>";
  const phoneNum = "18632664257";

  const data: {
    label: string;
    value: string;
    href?: string;
    sensitive?: boolean;
  }[] = [
    {
      label: "年龄",
      value: `${getMyAge("1993-07-13")}岁`,
    },
    { label: "现居住地", value: "山东青岛" },
    {
      label: "个人主页",
      value: homepage,
      href: homepage,
    },
    {
      label: "电话",
      value: phoneNum,
      href: `tel:${phoneNum}`,
      sensitive: true,
    },
    {
      label: "GitHub",
      value: githubLink,
      href: githubLink,
    },
    {
      label: "Email",
      value: email,
      href: `mailto:${email}`,
    },
  ];

  const isPrintMode = usePrintMode();

  return (
    <Section title="基本信息">
      <table>
        <tbody>
          {chunk(data, 2).map((c) => (
            <tr>
              {c.map((item) => (
                <td>
                  {item.label}
                  {": "}
                  {item.sensitive && !isPrintMode ? (
                    "*".repeat(Math.min(item.value.length, 11))
                  ) : item.href ? (
                    <a href={item.href} target="_blank">
                      {item.value}
                    </a>
                  ) : (
                    item.value
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </Section>
  );
}

function Section({ title, children }: PropsWithChildren<{ title: string }>) {
  return (
    <>
      <h2>{title}</h2>
      {children}
    </>
  );
}

function Education() {
  const data = [
    {
      school: "华北科技学院",
      major: "电子信息工程专业 (本科)",
      period: "2012-2016",
      website: "https://www.ncist.edu.cn",
    },
  ];

  return (
    <Section title="教育背景">
      <table>
        <tbody>
          {data.map((item) => (
            <>
              <tr>
                <td class="flex items-baseline">
                  <h3>{item.school}</h3>
                  <a class="ml-4" target="_blank" href={item.website}>
                    {item.website}
                  </a>
                </td>
              </tr>
              <tr>
                <td>{item.major}</td>
                <td class="text-right">
                  <em>{item.period}</em>
                </td>
              </tr>
            </>
          ))}
        </tbody>
      </table>
    </Section>
  );
}

function WorkExperiences() {
  const data: {
    company: string;
    timeRange?: string;
    description?: string;
    companyLink?: string;
    position?: string;
    jobs?: string[];
    location?: string;
  }[] = [
    {
      company: "--",
      position: "自由开发者",
      timeRange: `2023-02 至 ${currentMonth()}`,
      jobs: [
        "独立完成多个工具类和辅助性项目的设计与开发，后端接口搭建及前端实现。",
        "实践测试驱动开发（TDD），编写高覆盖率单元测试，具备 asyncio + ASGI 构建高并发服务的能力。",
        "使用 Jenkins + Docker Swarm 构建自动化部署流程。",
      ],
    },
    {
      company: "北京米可世界",
      location: "北京",
      description: "海外直播社交娱乐平台",
      timeRange: "2022-05 至 2023-02",
      position: "后端开发",
      companyLink: "https://www.micoworld.com",
      jobs: [
        "负责 Mico App 后台管理系统开发，包括用户管理、内容审核等模块。",
        "维护 Django + AngularJS 老系统，并逐步迁移到 Django + Vue 架构。",
        "自主开发了 Django-OpenAPI 开发工具，借助 OpenAPI 实现文档生成与自动化接口测试。",
      ],
    },
    {
      company: "北京回旋加速",
      location: "北京",
      description: "休闲游戏研发公司",
      position: "后端开发",
      companyLink: "https://www.huixuanjiasu.com",
      timeRange: "2021-03 至 2022-03",
      jobs: [
        "使用 Flask + MongoDB 快速开发游戏服务端，提供客户端接口。",
        "使用 Lazy object + contextvars 实现安全的上下文资源懒加载，减少初始化/释放负担，降低错误率。",
        "独立开发“约饭圈” Web 应用前端，使用 Vue2 框架，兼容移动与桌面端。",
      ],
    },
    {
      company: "Trucker Path (RenRen Inc.)",
      location: "北京",
      description: "美国运输网络公司",
      position: "Web 前端开发",
      companyLink: "https://truckerpath.com",
      timeRange: "2020-12 至 2021-03",
      jobs: [
        "使用 Vue2 完成 Fleet 车队管理平台 MVP 开发，支持车辆运输轨迹规划、任务发布、后台管理等模块。",
        "使用 Storybook 搭建并维护自定义组件库，提升组件复用效率。",
      ],
    },
    {
      company: "北京简网世纪",
      location: "北京",
      description: "提供本地生活服务平台",
      companyLink: "https://linkingcities.com",
      timeRange: "2015-11 至 2020-10",
      position: "内容运营 → 软件开发",
      jobs: [
        "早期担任平台内容编辑工作。2017年自学编程并转岗至技术团队，负责爬虫系统与前端开发工作。",
        "使用 Scrapy 开发爬虫系统，支持众多网站的内容采集；并通过逆向工程获取移动端 App 的内容。",
        "为 Hybrid App 开发 Webview 页面，提升 App 功能开发效率，采用 Vue2 + Webpack 多页架构。",
      ],
    },
  ];
  return (
    <Section title="工作经历">
      {data.map((item) => (
        <>
          <table>
            <tbody>
              <tr>
                <td>
                  <h3>
                    {item.company}

                    {item.position && (
                      <span className="font-normal text-[14px]">
                        <span className="mx-3">|</span>
                        {item.position}
                      </span>
                    )}

                    {item.location && (
                      <span className="font-normal text-[12px]">
                        <span className="mx-3">|</span>
                        <span className="inline-flex items-center">
                          <IoLocationOutline/></IoLocationOutline>
                          {item.location}
                        </span>
                      </span>
                    )}
                  </h3>
                  <p>
                    {item.description}{" "}
                    <a href={item.companyLink} target="_blank">
                      {item.companyLink}
                    </a>
                  </p>
                </td>
                {item.timeRange && (
                  <td className="text-right">
                    <em>{item.timeRange}</em>
                  </td>
                )}
              </tr>
            </tbody>
          </table>
          {item.jobs && item.jobs.length && (
            <ul>
              {item.jobs.map((job) => (
                <li>
                  <p>{job}</p>
                </li>
              ))}
            </ul>
          )}
        </>
      ))}
    </Section>
  );
}

function Skills() {
  const data: { label: string; values: string[] }[] = [
    {
      label: "前端",
      values: ["React", "Next.js", "Vue", "Typescript", "TailwindCSS"],
    },
    {
      label: "后端",
      values: [
        "Python",
        "Flask",
        "Django",
        "asyncio",
        "ASGI",
        "Redis",
        "PostgreSQL",
        "MongoDB",
      ],
    },
    { label: "工具", values: ["Docker", "Jenkins", "Git", "Nginx", "Sentry"] },
    { label: "测试", values: ["Pytest", "Vitest", "unittest", "coverage"] },
  ];
  return (
    <Section title="开发技能">
      {data.map((item) => (
        <p>
          {item.label}:{" "}
          {item.values.map((i) => (
            <code>{i}</code>
          ))}
        </p>
      ))}
    </Section>
  );
}

function OpenSourceProjects() {
  const djangoOasisURL = "https://dog-egg.github.io/django-oasis/";
  const zangarURL = "https://dog-egg.github.io/Zangar/";

  return (
    <Section title="开源项目">
      <ul>
        <li>
          Zangar:
          使用管道思想实现的数据校验与转换库，仅使用两个方法即可完成复杂的校验逻辑。
          <a target="_blank" href={zangarURL}>
            {zangarURL}
          </a>
        </li>
        <li>
          django-oasis: 基于 Django 和 OpenAPI
          的接口开发工具。提供请求验证、响应序列化、文档生成功能。
          <a target="_blank" href={djangoOasisURL}>
            {djangoOasisURL}
          </a>
        </li>
        <li>更多开源项目可查看我的个人主页和我的 Github。</li>
      </ul>
    </Section>
  );
}

render(<App />, document.getElementById("app")!);
