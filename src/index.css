@import url("https://fonts.googleapis.com/css?family=Poppins:400,400i,600,600i,700,700i,800,800i");
@import url("https://fonts.googleapis.com/css?family=Work+Sans:400,400i,500,500i,700,700i");
@import url("https://fonts.googleapis.com/css?family=Inter:400,400i,500,500i,600,600i,700,700i,800,800i");
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap");

@tailwind components;
@tailwind utilities;

body {
  padding: 48px;
  /* user-select: none; */
}
* {
  margin: 0;
  padding: 0;
}
html {
  font-size: 8px;
}
body {
  color: #222;
  font-family: "Work Sans", "Inter", "Noto Sans SC";
  font-size: 1.6rem;
  font-style: normal;
  font-weight: normal;
  line-height: 1.3;
  margin: auto;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", "Inter", "Noto Sans SC";
  font-weight: bold;
}
h1 {
  font-size: 3.6rem;
  font-weight: normal;
  margin-top: 0;
  text-align: left;
}
h2 {
  font-size: 2.4rem;
  margin-top: 1em;
}
h3 {
  font-size: 2rem;
  margin-top: 0.5em;
}
h4,
h5,
h6 {
  font-size: 1.8rem;
  margin-top: 0.4em;
}
b,
strong {
  font-weight: bold;
}
i,
em {
  font-style: italic;
}
ul,
ol {
  margin-top: 0.5em;
  list-style-type: none;
}
ul li:before,
ol li:before {
  color: #2ecc71;
  font-family: "Inter";
  font-weight: 600;
  content: "•";
  left: -12px;
  position: absolute;
}
h3 + ul {
  margin-top: 0;
}
a {
  color: #2ecc71;
  text-decoration: underline;
}
code {
  background-color: rgba(46, 204, 113, 0.25);
  font-family: inherit;
}
code + code {
  margin-left: 0.5em;
}
pre {
  font-family: inherit;
}
pre code {
  display: block;
}
p {
  margin-top: 0.5em;
}
p.pull-right {
  float: right;
  margin-right: 4px;
  margin-top: -1.3em;
  text-align: right;
}
h3 + p {
  margin-top: 0.2em;
}
li > p {
  margin-top: 0;
}
table {
  margin: 0;
  margin-top: 0.5em;
  table-layout: fixed;
  width: 100%;
}
table th,
table tr {
  font-weight: normal;
}
table td {
  padding-top: 0.25em;
  padding-bottom: 0.25em;
}
hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 1em 0;
}
::-moz-selection {
  background-color: rgba(46, 204, 113, 0.2);
}
::selection {
  background-color: rgba(46, 204, 113, 0.2);
}
h1 {
  font-weight: normal;
}
h2,
h4,
h5,
h6 {
  font-weight: 700;
}
h2 {
  border-bottom: 1px solid #ddd;
  position: relative;
  text-transform: uppercase;
}
h2:before {
  background-color: #2ecc71;
  bottom: -2px;
  content: "";
  height: 3px;
  left: 0;
  position: absolute;
  width: 48px;
}
h3 {
  font-weight: 600;
}
b,
strong {
  font-weight: 500;
}
h1 b,
h1 strong {
  font-weight: 700;
}
h2 b,
h2 strong,
h3 b,
h3 strong,
h4 b,
h4 strong,
h5 b,
h5 strong,
h6 b,
h6 strong {
  font-weight: 800;
}
ul {
  padding-left: 16px;
}
ul li {
  list-style-type: none;
  position: relative;
}
ul li:before {
  left: -16px;
  position: absolute;
}
a {
  color: #222;
  font-weight: 500;
}
code {
  background-color: rgba(46, 204, 113, 0.15) !important;
  border-radius: 2px;
  color: #1b7943;
  font-weight: 500;
  padding: 0 4px;
}
hr {
  border-top: 1px dashed #2ecc71;
}
