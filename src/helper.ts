export function getMyAge(birthday: string) {
  // 创建当前日期的 Date 对象
  let currentDate = new Date();

  // 创建特定日期的 Date 对象
  let birthdayDate = new Date(`${birthday}T00:00:00+08:00`);

  // 计算年份差
  let yearDifference = currentDate.getFullYear() - birthdayDate.getFullYear();

  // 检查月份、日期和时间，以确保准确的年份差
  if (
    currentDate.toISOString().substring(5) <
    birthdayDate.toISOString().substring(5)
  ) {
    yearDifference--;
  }

  // 返回年份差
  return yearDifference;
}

export function currentMonth() {
  const currentDate = new Date();

  const year = currentDate.getFullYear().toString();
  const month = (currentDate.getMonth() + 1).toString().padStart(2, "0");

  return `${year}-${month}`;
}
