import { test, expect, beforeEach, vi, afterEach } from "vitest";
import { getMyAge } from "./helper";

beforeEach(() => {
  vi.useFakeTimers();

  return () => vi.useRealTimers();
});

test("test getMyAge", () => {
  vi.setSystemTime(new Date("2024-07-13T00:00+08:00"));

  expect(getMyAge("1993-07-13")).toBe(31);
  expect(getMyAge("1993-07-12")).toBe(31);
  expect(getMyAge("1993-07-14")).toBe(30);
});
